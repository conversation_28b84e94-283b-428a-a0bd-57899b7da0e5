"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { ChevronRight } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background image - full coverage */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/background.png')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          width: "100%",
          height: "100%",
        }}
      ></div>

      {/* Header */}
      <header className="relative z-10 flex items-center justify-between px-6 py-4">
        <Link href="/" className="text-white text-xl font-bold font-clash">
          Salary 4 Life
        </Link>

        <nav className="hidden md:flex items-center space-x-8 text-gray-300">
          <Link href="/" className="hover:text-white transition-colors">
            Home
          </Link>
          <Link href="/partner" className="hover:text-white transition-colors">
            Partner
          </Link>
          <Link href="/faqs" className="hover:text-white transition-colors">
            FAQs
          </Link>
          <Link href="/community" className="hover:text-white transition-colors">
            Join Our Community
          </Link>
        </nav>

        <div className="flex items-center space-x-3">
          <Link href="/login">
            <Button
              variant="outline"
              className="bg-transparent border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              Log in
            </Button>
          </Link>
          <Link href="/register">
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
              Register
            </Button>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex items-center justify-center min-h-[calc(100vh-80px)] px-4">
        {/* Card with glowing border effect */}
        <div className="relative">
          {/* Glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 rounded-lg blur-sm opacity-75 animate-pulse"></div>

          <Card className="relative w-full max-w-md bg-black/80 backdrop-blur-sm border border-gray-800/50 shadow-2xl">
            <CardHeader className="text-center pb-6">
              <h1 className="text-2xl font-bold text-white mb-3">Register to play</h1>
              <p className="text-gray-400 text-sm leading-relaxed">
                Enter your details below and select th number of tickets you wish to purchase to take part in the Salary
                4 Life game show
              </p>
            </CardHeader>

            <CardContent className="space-y-6">
              <div>
                <label className="text-white font-medium mb-4 block">How many tickets do you want to buy?</label>
                <Input
                  placeholder="Full name"
                  className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-purple-500 focus:ring-purple-500/20"
                />
              </div>

              <div>
                <label className="text-white font-medium mb-3 block">Enter your phone number</label>
                <Input
                  placeholder="Full name"
                  className="bg-gray-900/50 border-gray-700 text-white placeholder-gray-500 focus:border-purple-500 focus:ring-purple-500/20"
                />
              </div>

              <div className="pt-4">
                <p className="text-gray-400 text-center mb-4">Continue payment with</p>

                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full bg-gray-100 hover:bg-gray-200 border-gray-300 text-black justify-between h-12"
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-purple-600 rounded mr-3 flex items-center justify-center">
                        <span className="text-white text-xs font-bold">$</span>
                      </div>
                      <span className="font-medium">USSD</span>
                    </div>
                    <ChevronRight className="w-4 h-4" />
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full bg-black hover:bg-gray-900 border-gray-700 text-white justify-between h-12"
                  >
                    <div className="flex items-center">
                      <div className="w-6 h-6 bg-green-500 rounded-full mr-3 flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                      <span className="font-medium">paystack</span>
                    </div>
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
