{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FClashDisplay-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-clash%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontClash%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22DM_Sans%5C%22%2C%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22Helvetica%20Neue%5C%22%2C%5C%22Arial%5C%22%2C%5C%22Noto%20Sans%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22Montserrat%5C%22%2C%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22Helvetica%20Neue%5C%22%2C%5C%22Arial%5C%22%2C%5C%22Noto%20Sans%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontMontserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"400\",\"variable\":\"--font-sans\",\"display\":\"swap\",\"adjustFontFallback\":false,\"fallback\":[\"Montserrat\",\"ui-sans-serif\",\"system-ui\",\"-apple-system\",\"BlinkMacSystemFont\",\"Segoe UI\",\"Roboto\",\"Helvetica Neue\",\"Arial\",\"Noto Sans\",\"sans-serif\",\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Noto Color Emoji\"]}],\"variableName\":\"fontMontserrat\"}"]}