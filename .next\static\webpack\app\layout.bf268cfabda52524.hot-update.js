/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9d185a330c12\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRWRkaWVcXERvY3VtZW50c1xcR2l0SHViXFx3aXNlLXdpbm4tYnV5LXRpY2tldHNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5ZDE4NWEzMzBjMTJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FClashDisplay-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-clash%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontClash%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22DM_Sans%5C%22%2C%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22Helvetica%20Neue%5C%22%2C%5C%22Arial%5C%22%2C%5C%22Noto%20Sans%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FClashDisplay-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-clash%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontClash%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22DM_Sans%5C%22%2C%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22Helvetica%20Neue%5C%22%2C%5C%22Arial%5C%22%2C%5C%22Noto%20Sans%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/local/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":\"./fonts/ClashDisplay-Variable.woff2\",\"variable\":\"--font-clash\",\"display\":\"swap\"}],\"variableName\":\"fontClash\"} */ \"(app-pages-browser)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":\\\"./fonts/ClashDisplay-Variable.woff2\\\",\\\"variable\\\":\\\"--font-clash\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"fontClash\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":\"400\",\"variable\":\"--font-sans\",\"display\":\"swap\",\"adjustFontFallback\":false,\"fallback\":[\"DM_Sans\",\"ui-sans-serif\",\"system-ui\",\"-apple-system\",\"BlinkMacSystemFont\",\"Segoe UI\",\"Roboto\",\"Helvetica Neue\",\"Arial\",\"Noto Sans\",\"sans-serif\",\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Noto Color Emoji\"]}],\"variableName\":\"fontSans\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":\\\"400\\\",\\\"variable\\\":\\\"--font-sans\\\",\\\"display\\\":\\\"swap\\\",\\\"adjustFontFallback\\\":false,\\\"fallback\\\":[\\\"DM_Sans\\\",\\\"ui-sans-serif\\\",\\\"system-ui\\\",\\\"-apple-system\\\",\\\"BlinkMacSystemFont\\\",\\\"Segoe UI\\\",\\\"Roboto\\\",\\\"Helvetica Neue\\\",\\\"Arial\\\",\\\"Noto Sans\\\",\\\"sans-serif\\\",\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\",\\\"Noto Color Emoji\\\"]}],\\\"variableName\\\":\\\"fontSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5C%22.%2Ffonts%2FClashDisplay-Variable.woff2%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-clash%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontClash%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22adjustFontFallback%5C%22%3Afalse%2C%5C%22fallback%5C%22%3A%5B%5C%22DM_Sans%5C%22%2C%5C%22ui-sans-serif%5C%22%2C%5C%22system-ui%5C%22%2C%5C%22-apple-system%5C%22%2C%5C%22BlinkMacSystemFont%5C%22%2C%5C%22Segoe%20UI%5C%22%2C%5C%22Roboto%5C%22%2C%5C%22Helvetica%20Neue%5C%22%2C%5C%22Arial%5C%22%2C%5C%22Noto%20Sans%5C%22%2C%5C%22sans-serif%5C%22%2C%5C%22Apple%20Color%20Emoji%5C%22%2C%5C%22Segoe%20UI%20Emoji%5C%22%2C%5C%22Noto%20Color%20Emoji%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22fontSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CEddie%5C%5CDocuments%5C%5CGitHub%5C%5Cwise-winn-buy-tickets%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ })

});