{"name": "input-otp", "version": "1.4.1", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "description": "One-time password input component for React.", "license": "MIT", "homepage": "https://input-otp.rodz.dev/", "repository": {"type": "git", "url": "git+https://github.com/guilhermerodz/input-otp.git", "directory": "packages/input-otp"}, "bugs": {"url": "https://github.com/guilhermerodz/input-otp/issues"}, "keywords": ["react", "otp", "input", "accessible"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "module": "./dist/index.mjs", "import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.mjs"}, "./package.json": "./package.json"}, "scripts": {"type-check": "tsc --noEmit", "copy-readme": "cp ../../README.md ./README.md", "build": "run-s build:* copy-readme", "build:tsup": "tsup --dts --minify", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsup --watch --dts", "lint": "run-p lint:*", "lint:eslint": "eslint src --ext .ts", "lint:eslint:fix": "eslint src --ext .ts --fix", "lint:format": "prettier --check \"src/**/*.ts\"", "lint:format:fix": "prettier --check \"src/**/*.ts\" --write", "lint:tsc": "tsc --project tsconfig.json --noEmit", "format": "prettier --write .", "release": "npm publish", "release:beta": "npm publish --tag beta"}, "eslintConfig": {"root": true, "reportUnusedDisableDirectives": true, "ignorePatterns": ["**/build", "**/coverage", "**/dist"], "parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}, "settings": {"import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": true}, "react": {"version": "detect"}}, "plugins": ["@typescript-eslint", "import"], "extends": ["plugin:react/jsx-runtime", "plugin:react-hooks/recommended", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/stylistic", "plugin:import/recommended", "plugin:import/typescript", "prettier"], "env": {"browser": true, "es2020": true}, "rules": {"react/jsx-key": ["error", {"checkFragmentShorthand": true}], "react-hooks/exhaustive-deps": "error", "@typescript-eslint/no-explicit-any": "off"}}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"}}