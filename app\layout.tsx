import type { <PERSON><PERSON><PERSON> } from 'next'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import localFont from "next/font/local";



import './globals.css'
import { cn } from '@/lib/utils';

const fontClash = localFont({
  src: "./fonts/ClashDisplay-Variable.woff2",
  variable: "--font-clash",
  display: "swap",
});

const fontSans = Poppins({
  subsets: ['latin'],
  weight: '400',
  variable: '--font-sans',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Poppins',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});


const fontMontserrat = Montserrat({
  subsets: ['latin'],
  weight: '400',
  variable: '--font-montserrat',
  display: 'swap',
  adjustFontFallback: false,
  fallback: [
    'Montserrat',
    'ui-sans-serif',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Noto Color Emoji',
  ],
});



export const metadata: Metadata = {
  title: "Salary 4 Life",
  description: "Win a salary for life by participating in our game show",
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={cn(fontSans.className, fontClash.variable, fontMontserrat.variable)}>{children}</body>
    </html>
  )
}
